{"cells": [{"cell_type": "code", "execution_count": 1, "id": "558<PERSON><PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["from pathlib import Path\n", "from faninsar import datasets, query"]}, {"cell_type": "code", "execution_count": null, "id": "8ba8953e", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 2, "id": "7ac1b231", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Volumes/Data/Github/FanInSAR/faninsar/datasets/ifg.py:241: UserWarning: Duplicate pairs found in dataset unwrapped interferograms, keeping only the first occurrence\n", "Deduplicate pairs: \n", "\tS1AA_20191115T232700_20200314T232657_VVP120_INT40_G_ueF_F25B\n", "\tS1AA_20221018T232718_20221111T232718_VVP024_INT40_G_ueF_C5D1\n", "  paths_unw, pairs_unw = self._deduplicate_pairs(\n", "/Volumes/Data/Github/FanInSAR/faninsar/datasets/ifg.py:245: UserWarning: Duplicate pairs found in dataset coherence files, keeping only the first occurrence\n", "Deduplicate pairs: \n", "\tS1AA_20191115T232700_20200314T232657_VVP120_INT40_G_ueF_F25B\n", "\tS1AA_20221018T232718_20221111T232718_VVP024_INT40_G_ueF_C5D1\n", "  paths_coh, pairs_coh = self._deduplicate_pairs(paths_coh, \"coherence files\")\n", "Scanning Interferogram files: 100%|██████████| 2375/2375 [00:00<00:00, 5129491.25 files/s]\n", "0.00s - Debugger warning: It seems that frozen modules are being used, which may\n", "0.00s - make the debugger miss breakpoints. Please pass -Xfrozen_modules=off\n", "0.00s - to python to disable frozen modules.\n", "0.00s - Note: Debugging will proceed. Set PYDEVD_DISABLE_FILE_VALIDATION=1 to disable this validation.\n", "ERROR:tornado.general:<PERSON><PERSON> Error: Host unreachable\n"]}, {"ename": "TypeError", "evalue": "object of type 'NoneType' has no len()", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mType<PERSON>rror\u001b[39m                                 <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m data_dir = Path(\n\u001b[32m      2\u001b[39m     \u001b[33m\"\u001b[39m\u001b[33m/Volumes/Data/GeoData/YNG/Sentinel1/Hyp3/descending_roi/nearest_connection\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m      3\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m ds = \u001b[43mdatasets\u001b[49m\u001b[43m.\u001b[49m\u001b[43mHyP3S1\u001b[49m\u001b[43m(\u001b[49m\u001b[43mroot_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdata_dir\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mparallel_loading\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/Volumes/Data/Github/FanInSAR/faninsar/datasets/ifg.py:263\u001b[39m, in \u001b[36mInterferogramDataset.__init__\u001b[39m\u001b[34m(self, root_dir, paths_unw, paths_coh, crs, res, dtype, nodata, roi, bands_unw, bands_coh, cache, resampling, fill_nodata, verbose, keep_common, parallel_loading)\u001b[39m\n\u001b[32m    260\u001b[39m     paths_unw = paths_unw[pairs_unw.where(pairs)]\n\u001b[32m    261\u001b[39m     paths_coh = paths_coh[pairs_coh.where(pairs)]\n\u001b[32m--> \u001b[39m\u001b[32m263\u001b[39m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    264\u001b[39m \u001b[43m    \u001b[49m\u001b[43mroot_dir\u001b[49m\u001b[43m=\u001b[49m\u001b[43mroot_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    265\u001b[39m \u001b[43m    \u001b[49m\u001b[43mpaths\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpaths_unw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    266\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcrs\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcrs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    267\u001b[39m \u001b[43m    \u001b[49m\u001b[43mres\u001b[49m\u001b[43m=\u001b[49m\u001b[43mres\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    268\u001b[39m \u001b[43m    \u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m=\u001b[49m\u001b[43mdtype\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    269\u001b[39m \u001b[43m    \u001b[49m\u001b[43mnodata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mnodata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    270\u001b[39m \u001b[43m    \u001b[49m\u001b[43mroi\u001b[49m\u001b[43m=\u001b[49m\u001b[43mroi\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    271\u001b[39m \u001b[43m    \u001b[49m\u001b[43mbands\u001b[49m\u001b[43m=\u001b[49m\u001b[43mbands_unw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    272\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcache\u001b[49m\u001b[43m=\u001b[49m\u001b[43mcache\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    273\u001b[39m \u001b[43m    \u001b[49m\u001b[43mresampling\u001b[49m\u001b[43m=\u001b[49m\u001b[43mresampling\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    274\u001b[39m \u001b[43m    \u001b[49m\u001b[43mfill_nodata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mfill_nodata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    275\u001b[39m \u001b[43m    \u001b[49m\u001b[43mverbose\u001b[49m\u001b[43m=\u001b[49m\u001b[43mverbose\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    276\u001b[39m \u001b[43m    \u001b[49m\u001b[43mds_name\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mInterferogram\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    277\u001b[39m \u001b[43m    \u001b[49m\u001b[43mparallel_loading\u001b[49m\u001b[43m=\u001b[49m\u001b[43mparallel_loading\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    278\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    280\u001b[39m \u001b[38;5;28mself\u001b[39m._ds_coh = CoherenceDataset(\n\u001b[32m    281\u001b[39m     root_dir=root_dir,\n\u001b[32m    282\u001b[39m     paths=paths_coh,\n\u001b[32m   (...)\u001b[39m\u001b[32m    294\u001b[39m     parallel_loading=parallel_loading,\n\u001b[32m    295\u001b[39m )\n\u001b[32m    296\u001b[39m \u001b[38;5;28mself\u001b[39m._ds_coh._range = \u001b[38;5;28mself\u001b[39m.coh_range\n", "\u001b[36mFile \u001b[39m\u001b[32m/Volumes/Data/Github/FanInSAR/faninsar/datasets/base.py:2494\u001b[39m, in \u001b[36mPairDataset.__init__\u001b[39m\u001b[34m(self, *args, **kwargs)\u001b[39m\n\u001b[32m   2491\u001b[39m \u001b[38;5;129m@abstractmethod\u001b[39m\n\u001b[32m   2492\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m__init__\u001b[39m(\u001b[38;5;28mself\u001b[39m, *args, **kwargs) -> \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m   2493\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"Initialize the dataset. *Must be implemented in subclass*.\"\"\"\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m2494\u001b[39m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[34;43m__init__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/Volumes/Data/Github/FanInSAR/faninsar/datasets/base.py:722\u001b[39m, in \u001b[36mRasterDataset.__init__\u001b[39m\u001b[34m(self, root_dir, paths, crs, res, dtype, nodata, roi, bands, cache, resampling, fill_nodata, verbose, ds_name, parallel_loading)\u001b[39m\n\u001b[32m    720\u001b[39m \u001b[38;5;66;03m# Set final attributes\u001b[39;00m\n\u001b[32m    721\u001b[39m \u001b[38;5;28mself\u001b[39m.crs = crs\n\u001b[32m--> \u001b[39m\u001b[32m722\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mres\u001b[49m = res\n\u001b[32m    723\u001b[39m \u001b[38;5;28mself\u001b[39m.dtype = dtype\n\u001b[32m    724\u001b[39m \u001b[38;5;28mself\u001b[39m.nodata = nodata\n", "\u001b[36mFile \u001b[39m\u001b[32m/Volumes/Data/Github/FanInSAR/faninsar/datasets/base.py:303\u001b[39m, in \u001b[36mGeoDataset.res\u001b[39m\u001b[34m(self, new_res)\u001b[39m\n\u001b[32m    301\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(new_res, (\u001b[38;5;28mint\u001b[39m, \u001b[38;5;28mfloat\u001b[39m, np.integer, np.floating)):\n\u001b[32m    302\u001b[39m     new_res = (\u001b[38;5;28mfloat\u001b[39m(new_res), \u001b[38;5;28mfloat\u001b[39m(new_res))\n\u001b[32m--> \u001b[39m\u001b[32m303\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mnew_res\u001b[49m\u001b[43m)\u001b[49m != \u001b[32m2\u001b[39m:\n\u001b[32m    304\u001b[39m     msg = \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mResolution must be a float or a tuple of length 2, got \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mnew_res\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m\n\u001b[32m    305\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    306\u001b[39m         msg,\n\u001b[32m    307\u001b[39m     )\n", "\u001b[31mTypeError\u001b[39m: object of type 'NoneType' has no len()"]}], "source": ["data_dir = Path(\n", "    \"/Volumes/Data/GeoData/YNG/Sentinel1/Hyp3/descending_roi/nearest_connection\"\n", ")\n", "ds = datasets.HyP3S1(root_dir=data_dir, parallel_loading=True)"]}, {"cell_type": "code", "execution_count": null, "id": "01736f3c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DatasetView&gt; Size: 0B\n", "Dimensions:  ()\n", "Data variables:\n", "    *empty*\n", "Attributes:\n", "    query_type:  GeoQuery</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataTree</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-935bc853-3a7b-46c0-82ea-26b36db31015' class='xr-section-summary-in' type='checkbox'  ><label for='section-935bc853-3a7b-46c0-82ea-26b36db31015' class='xr-section-summary' >Groups: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style='display: inline-grid; grid-template-columns: 100%; grid-column: 1 / -1'><div style='display: inline-grid; grid-template-columns: 0px 20px auto; width: 100%;'><div style='grid-column-start: 1;border-right: 0.2em solid;border-color: var(--xr-border-color);height: 1.2em;width: 0px;'></div><div style='grid-column-start: 2;grid-row-start: 1;height: 1em;width: 20px;border-bottom: 0.2em solid;border-color: var(--xr-border-color);'></div><div style='grid-column-start: 3;'><div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DatasetView&gt; Size: 135MB\n", "Dimensions:  (files: 2375, y: 147, x: 97)\n", "Coordinates:\n", "  * files    (files) int64 19kB 0 1 2 3 4 5 6 ... 2369 2370 2371 2372 2373 2374\n", "  * y        (y) int64 1kB 0 1 2 3 4 5 6 7 8 ... 139 140 141 142 143 144 145 146\n", "  * x        (x) int64 776B 0 1 2 3 4 5 6 7 8 9 ... 88 89 90 91 92 93 94 95 96\n", "Data variables:\n", "    values   (files, y, x) float32 135MB -11.49 -11.85 -11.67 ... 11.43 11.29</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>boxes</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-e2c5ed45-2b4b-4485-9cff-9c6de2c6bfd2' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-e2c5ed45-2b4b-4485-9cff-9c6de2c6bfd2' class='xr-section-summary'  title='Expand/collapse section'>Groups: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><div style='display: inline-grid; grid-template-columns: 100%; grid-column: 1 / -1'></div></div></li><li class='xr-section-item'><input id='section-33e7626c-5855-467c-b279-1446ed23e1e8' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-33e7626c-5855-467c-b279-1446ed23e1e8' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>files</span>: 2375</li><li><span class='xr-has-index'>y</span>: 147</li><li><span class='xr-has-index'>x</span>: 97</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-4163e278-1f9e-4601-9c59-c180b5c3140e' class='xr-section-summary-in' type='checkbox'  checked><label for='section-4163e278-1f9e-4601-9c59-c180b5c3140e' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>files</span></div><div class='xr-var-dims'>(files)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 ... 2371 2372 2373 2374</div><input id='attrs-6bce0d8f-731a-4c88-8035-a242aabf0946' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-6bce0d8f-731a-4c88-8035-a242aabf0946' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-96fc1707-728b-42ca-8349-5b3a874492d0' class='xr-var-data-in' type='checkbox'><label for='data-96fc1707-728b-42ca-8349-5b3a874492d0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([   0,    1,    2, ..., 2372, 2373, 2374], shape=(2375,))</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 5 ... 142 143 144 145 146</div><input id='attrs-3c316650-3876-45e2-80aa-9fd11ac153ad' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3c316650-3876-45e2-80aa-9fd11ac153ad' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-28ffd313-9bc6-4171-b561-907557d4a0f3' class='xr-var-data-in' type='checkbox'><label for='data-28ffd313-9bc6-4171-b561-907557d4a0f3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0,   1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,\n", "        14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,\n", "        28,  29,  30,  31,  32,  33,  34,  35,  36,  37,  38,  39,  40,  41,\n", "        42,  43,  44,  45,  46,  47,  48,  49,  50,  51,  52,  53,  54,  55,\n", "        56,  57,  58,  59,  60,  61,  62,  63,  64,  65,  66,  67,  68,  69,\n", "        70,  71,  72,  73,  74,  75,  76,  77,  78,  79,  80,  81,  82,  83,\n", "        84,  85,  86,  87,  88,  89,  90,  91,  92,  93,  94,  95,  96,  97,\n", "        98,  99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111,\n", "       112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125,\n", "       126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139,\n", "       140, 141, 142, 143, 144, 145, 146])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 5 6 ... 91 92 93 94 95 96</div><input id='attrs-ecf24503-ad58-4b22-9b50-01f014988533' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ecf24503-ad58-4b22-9b50-01f014988533' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e6529b8f-a318-456e-9552-cbafdb2595d2' class='xr-var-data-in' type='checkbox'><label for='data-e6529b8f-a318-456e-9552-cbafdb2595d2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,\n", "       18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,\n", "       36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53,\n", "       54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71,\n", "       72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89,\n", "       90, 91, 92, 93, 94, 95, 96])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-1b66d04a-6507-40d6-b5ba-fe6752c46203' class='xr-section-summary-in' type='checkbox'  checked><label for='section-1b66d04a-6507-40d6-b5ba-fe6752c46203' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>values</span></div><div class='xr-var-dims'>(files, y, x)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>-11.49 -11.85 ... 11.43 11.29</div><input id='attrs-71dee37d-2751-4453-b79a-f7e686341119' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-71dee37d-2751-4453-b79a-f7e686341119' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-23ef4328-6d96-4051-8151-6de015af0ae3' class='xr-var-data-in' type='checkbox'><label for='data-23ef4328-6d96-4051-8151-6de015af0ae3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[-11.493204  , -11.847656  , -11.667929  , ..., -12.484718  ,\n", "         -12.184986  , -12.281435  ],\n", "        [-11.62019   , -11.766399  , -11.660206  , ..., -11.939253  ,\n", "         -12.241682  , -12.658979  ],\n", "        [-12.988731  , -12.2572365 , -11.178396  , ..., -11.981106  ,\n", "         -11.994528  , -12.050138  ],\n", "        ...,\n", "        [-14.2327    , -13.273895  , -15.700272  , ..., -13.494963  ,\n", "         -13.900658  , -13.273582  ],\n", "        [-17.197681  , -12.614498  , -15.126915  , ..., -14.126675  ,\n", "         -13.153246  , -12.891045  ],\n", "        [-14.343983  , -15.169006  , -13.192183  , ..., -14.635809  ,\n", "         -14.141075  , -14.314266  ]],\n", "\n", "       [[ -7.6382856 ,  -8.50587   ,  -8.250343  , ...,  -6.6263776 ,\n", "          -6.9525304 ,  -6.3186884 ],\n", "        [ -6.9765463 ,  -7.631773  ,  -6.6125135 , ...,  -5.9198694 ,\n", "          -6.773549  ,  -6.21879   ],\n", "        [ -5.633887  ,  -7.755252  ,  -5.075944  , ...,  -6.1245317 ,\n", "          -6.090739  ,  -6.0760775 ],\n", "...\n", "           8.990334  ,  10.250841  ],\n", "        [ 13.827702  ,   9.535217  ,   9.222277  , ...,  14.840733  ,\n", "          10.843645  ,   9.085852  ],\n", "        [         nan,  11.18679   ,  13.499115  , ...,  12.479061  ,\n", "                  nan,  10.266909  ]],\n", "\n", "       [[ 11.568005  ,  11.564882  ,  11.544861  , ...,  10.960449  ,\n", "          10.711256  ,  11.038464  ],\n", "        [ 11.516882  ,  11.463957  ,  11.441393  , ...,  11.217278  ,\n", "          11.045567  ,  11.091274  ],\n", "        [ 11.589649  ,  11.542107  ,  11.49753   , ...,  10.964664  ,\n", "          10.980946  ,  11.121391  ],\n", "        ...,\n", "        [ 11.317413  ,  11.246052  ,  11.280352  , ...,  11.496363  ,\n", "          11.549274  ,  11.365282  ],\n", "        [ 11.267599  ,  11.327505  ,  11.30798   , ...,  11.439623  ,\n", "          11.366547  ,  11.207685  ],\n", "        [ 11.394686  ,  11.315868  ,  11.389692  , ...,  11.437691  ,\n", "          11.434668  ,  11.286449  ]]],\n", "      shape=(2375, 147, 97), dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-88ddac5e-57a0-4353-9917-1ab9e3bf7a96' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-88ddac5e-57a0-4353-9917-1ab9e3bf7a96' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div></div></div></div></div></li><li class='xr-section-item'><input id='section-e199b4ad-4df8-441a-8912-ca2f421db729' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-e199b4ad-4df8-441a-8912-ca2f421db729' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-05344014-8708-43c7-8f86-9593d3565554' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-05344014-8708-43c7-8f86-9593d3565554' class='xr-section-summary'  title='Expand/collapse section'>Coordinates: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'></ul></div></li><li class='xr-section-item'><input id='section-f80d7ca5-41a6-419e-94db-c3da8a646692' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-f80d7ca5-41a6-419e-94db-c3da8a646692' class='xr-section-summary'  title='Expand/collapse section'>Inherited coordinates: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'></ul></div></li><li class='xr-section-item'><input id='section-5a948cf7-7cfc-4814-9e2c-63c757b83c0f' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-5a948cf7-7cfc-4814-9e2c-63c757b83c0f' class='xr-section-summary'  title='Expand/collapse section'>Data variables: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'></ul></div></li><li class='xr-section-item'><input id='section-2eba5ed8-5f50-40ef-a13f-664c1e3b06b9' class='xr-section-summary-in' type='checkbox'  checked><label for='section-2eba5ed8-5f50-40ef-a13f-664c1e3b06b9' class='xr-section-summary' >Attributes: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>query_type :</span></dt><dd>GeoQuery</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataTree 'query_result'>\n", "Group: /\n", "│   Attributes:\n", "│       query_type:  GeoQuery\n", "└── Group: /boxes\n", "        Dimensions:  (files: 2375, y: 147, x: 97)\n", "        Coordinates:\n", "          * files    (files) int64 19kB 0 1 2 3 4 5 6 ... 2369 2370 2371 2372 2373 2374\n", "          * y        (y) int64 1kB 0 1 2 3 4 5 6 7 8 ... 139 140 141 142 143 144 145 146\n", "          * x        (x) int64 776B 0 1 2 3 4 5 6 7 8 9 ... 88 89 90 91 92 93 94 95 96\n", "        Data variables:\n", "            values   (files, y, x) float32 135MB -11.49 -11.85 -11.67 ... 11.43 11.29"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["roi = query.BoundingBox(98.86517887, 38.78630936, 98.90998476, 38.83929150, crs=4326)\n", "result = ds.query(roi, use_dask=True)\n", "result"]}, {"cell_type": "code", "execution_count": null, "id": "e5604084", "metadata": {}, "outputs": [{"data": {"text/plain": ["0.12617693468928337"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["result.nbytes / 1024**3"]}, {"cell_type": "code", "execution_count": null, "id": "a0cffb05", "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=\"dark\"],\n", "html[data-theme=\"dark\"],\n", "body[data-theme=\"dark\"],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1f1f1f;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 0 20px 0 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: inline-block;\n", "  opacity: 0;\n", "  height: 0;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:focus + label {\n", "  border: 2px solid var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: \"►\";\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: \"▼\";\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: \"(\";\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: \")\";\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: \",\";\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt; Size: 135MB\n", "Dimensions:  (files: 2375, y: 147, x: 97)\n", "Coordinates:\n", "  * files    (files) int64 19kB 0 1 2 3 4 5 6 ... 2369 2370 2371 2372 2373 2374\n", "  * y        (y) int64 1kB 0 1 2 3 4 5 6 7 8 ... 139 140 141 142 143 144 145 146\n", "  * x        (x) int64 776B 0 1 2 3 4 5 6 7 8 9 ... 88 89 90 91 92 93 94 95 96\n", "Data variables:\n", "    values   (files, y, x) float32 135MB -11.49 -11.85 -11.67 ... 11.43 11.29</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-764b44e1-1742-4f18-a88e-6843a7f94881' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-764b44e1-1742-4f18-a88e-6843a7f94881' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>files</span>: 2375</li><li><span class='xr-has-index'>y</span>: 147</li><li><span class='xr-has-index'>x</span>: 97</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-edd586d9-5cf1-495e-80be-bc01c36a7ea5' class='xr-section-summary-in' type='checkbox'  checked><label for='section-edd586d9-5cf1-495e-80be-bc01c36a7ea5' class='xr-section-summary' >Coordinates: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>files</span></div><div class='xr-var-dims'>(files)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 ... 2371 2372 2373 2374</div><input id='attrs-5fdd7f34-ed19-45e2-bb4a-49e3a6b2a19b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-5fdd7f34-ed19-45e2-bb4a-49e3a6b2a19b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-425dc606-590b-4ea4-ae22-5b2cafba099a' class='xr-var-data-in' type='checkbox'><label for='data-425dc606-590b-4ea4-ae22-5b2cafba099a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([   0,    1,    2, ..., 2372, 2373, 2374], shape=(2375,))</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 5 ... 142 143 144 145 146</div><input id='attrs-787363ca-dd54-4cd2-b2bf-c27b0de29fe3' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-787363ca-dd54-4cd2-b2bf-c27b0de29fe3' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ab344923-1cc5-4242-a52b-e8e7790e727e' class='xr-var-data-in' type='checkbox'><label for='data-ab344923-1cc5-4242-a52b-e8e7790e727e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([  0,   1,   2,   3,   4,   5,   6,   7,   8,   9,  10,  11,  12,  13,\n", "        14,  15,  16,  17,  18,  19,  20,  21,  22,  23,  24,  25,  26,  27,\n", "        28,  29,  30,  31,  32,  33,  34,  35,  36,  37,  38,  39,  40,  41,\n", "        42,  43,  44,  45,  46,  47,  48,  49,  50,  51,  52,  53,  54,  55,\n", "        56,  57,  58,  59,  60,  61,  62,  63,  64,  65,  66,  67,  68,  69,\n", "        70,  71,  72,  73,  74,  75,  76,  77,  78,  79,  80,  81,  82,  83,\n", "        84,  85,  86,  87,  88,  89,  90,  91,  92,  93,  94,  95,  96,  97,\n", "        98,  99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111,\n", "       112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125,\n", "       126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139,\n", "       140, 141, 142, 143, 144, 145, 146])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0 1 2 3 4 5 6 ... 91 92 93 94 95 96</div><input id='attrs-1497b062-e59e-4858-a38f-976006870cfa' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-1497b062-e59e-4858-a38f-976006870cfa' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5a97cc4b-9493-4669-9b11-e72187c9439f' class='xr-var-data-in' type='checkbox'><label for='data-5a97cc4b-9493-4669-9b11-e72187c9439f' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,\n", "       18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,\n", "       36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53,\n", "       54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71,\n", "       72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89,\n", "       90, 91, 92, 93, 94, 95, 96])</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-d1b2a90f-2ec3-42f6-99a5-d9058e5a2591' class='xr-section-summary-in' type='checkbox'  checked><label for='section-d1b2a90f-2ec3-42f6-99a5-d9058e5a2591' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>values</span></div><div class='xr-var-dims'>(files, y, x)</div><div class='xr-var-dtype'>float32</div><div class='xr-var-preview xr-preview'>-11.49 -11.85 ... 11.43 11.29</div><input id='attrs-02dc8215-2eb8-445a-94e4-85cbfbcb7d97' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-02dc8215-2eb8-445a-94e4-85cbfbcb7d97' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2a8ceaee-19ff-42a1-891d-547160cd9480' class='xr-var-data-in' type='checkbox'><label for='data-2a8ceaee-19ff-42a1-891d-547160cd9480' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([[[-11.493204  , -11.847656  , -11.667929  , ..., -12.484718  ,\n", "         -12.184986  , -12.281435  ],\n", "        [-11.62019   , -11.766399  , -11.660206  , ..., -11.939253  ,\n", "         -12.241682  , -12.658979  ],\n", "        [-12.988731  , -12.2572365 , -11.178396  , ..., -11.981106  ,\n", "         -11.994528  , -12.050138  ],\n", "        ...,\n", "        [-14.2327    , -13.273895  , -15.700272  , ..., -13.494963  ,\n", "         -13.900658  , -13.273582  ],\n", "        [-17.197681  , -12.614498  , -15.126915  , ..., -14.126675  ,\n", "         -13.153246  , -12.891045  ],\n", "        [-14.343983  , -15.169006  , -13.192183  , ..., -14.635809  ,\n", "         -14.141075  , -14.314266  ]],\n", "\n", "       [[ -7.6382856 ,  -8.50587   ,  -8.250343  , ...,  -6.6263776 ,\n", "          -6.9525304 ,  -6.3186884 ],\n", "        [ -6.9765463 ,  -7.631773  ,  -6.6125135 , ...,  -5.9198694 ,\n", "          -6.773549  ,  -6.21879   ],\n", "        [ -5.633887  ,  -7.755252  ,  -5.075944  , ...,  -6.1245317 ,\n", "          -6.090739  ,  -6.0760775 ],\n", "...\n", "           8.990334  ,  10.250841  ],\n", "        [ 13.827702  ,   9.535217  ,   9.222277  , ...,  14.840733  ,\n", "          10.843645  ,   9.085852  ],\n", "        [         nan,  11.18679   ,  13.499115  , ...,  12.479061  ,\n", "                  nan,  10.266909  ]],\n", "\n", "       [[ 11.568005  ,  11.564882  ,  11.544861  , ...,  10.960449  ,\n", "          10.711256  ,  11.038464  ],\n", "        [ 11.516882  ,  11.463957  ,  11.441393  , ...,  11.217278  ,\n", "          11.045567  ,  11.091274  ],\n", "        [ 11.589649  ,  11.542107  ,  11.49753   , ...,  10.964664  ,\n", "          10.980946  ,  11.121391  ],\n", "        ...,\n", "        [ 11.317413  ,  11.246052  ,  11.280352  , ...,  11.496363  ,\n", "          11.549274  ,  11.365282  ],\n", "        [ 11.267599  ,  11.327505  ,  11.30798   , ...,  11.439623  ,\n", "          11.366547  ,  11.207685  ],\n", "        [ 11.394686  ,  11.315868  ,  11.389692  , ...,  11.437691  ,\n", "          11.434668  ,  11.286449  ]]],\n", "      shape=(2375, 147, 97), dtype=float32)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-6d12d425-b0a5-4d48-acfe-8f69f423b4c0' class='xr-section-summary-in' type='checkbox'  ><label for='section-6d12d425-b0a5-4d48-acfe-8f69f423b4c0' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>files</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-cc2f1858-518a-48b0-bb40-d20546dc5b99' class='xr-index-data-in' type='checkbox'/><label for='index-cc2f1858-518a-48b0-bb40-d20546dc5b99' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([   0,    1,    2,    3,    4,    5,    6,    7,    8,    9,\n", "       ...\n", "       2365, 2366, 2367, 2368, 2369, 2370, 2371, 2372, 2373, 2374],\n", "      dtype=&#x27;int64&#x27;, name=&#x27;files&#x27;, length=2375))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-66fbb9e3-b9a9-43bd-8661-f45f511130c3' class='xr-index-data-in' type='checkbox'/><label for='index-66fbb9e3-b9a9-43bd-8661-f45f511130c3' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([  0,   1,   2,   3,   4,   5,   6,   7,   8,   9,\n", "       ...\n", "       137, 138, 139, 140, 141, 142, 143, 144, 145, 146],\n", "      dtype=&#x27;int64&#x27;, name=&#x27;y&#x27;, length=147))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><input type='checkbox' disabled/><label></label><input id='index-cf1d33a5-5997-49d1-a4bc-db2ccbca5145' class='xr-index-data-in' type='checkbox'/><label for='index-cf1d33a5-5997-49d1-a4bc-db2ccbca5145' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([ 0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17,\n", "       18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35,\n", "       36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53,\n", "       54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71,\n", "       72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89,\n", "       90, 91, 92, 93, 94, 95, 96],\n", "      dtype=&#x27;int64&#x27;, name=&#x27;x&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-eae109d3-bcd6-469c-8ee4-7f57c09e10fe' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-eae109d3-bcd6-469c-8ee4-7f57c09e10fe' class='xr-section-summary'  title='Expand/collapse section'>Attributes: <span>(0)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset> Size: 135MB\n", "Dimensions:  (files: 2375, y: 147, x: 97)\n", "Coordinates:\n", "  * files    (files) int64 19kB 0 1 2 3 4 5 6 ... 2369 2370 2371 2372 2373 2374\n", "  * y        (y) int64 1kB 0 1 2 3 4 5 6 7 8 ... 139 140 141 142 143 144 145 146\n", "  * x        (x) int64 776B 0 1 2 3 4 5 6 7 8 9 ... 88 89 90 91 92 93 94 95 96\n", "Data variables:\n", "    values   (files, y, x) float32 135MB -11.49 -11.85 -11.67 ... 11.43 11.29"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": ["result.boxes"]}, {"cell_type": "code", "execution_count": null, "id": "fd783cc5", "metadata": {}, "outputs": [{"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}, {"ename": "", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31mnotebook controller is DISPOSED. \n", "\u001b[1;31m<PERSON><PERSON><PERSON> <a href='command:jupyter.viewOutput'>log</a> for further details."]}], "source": []}], "metadata": {"kernelspec": {"display_name": "faninsar_dev", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.2"}}, "nbformat": 4, "nbformat_minor": 5}