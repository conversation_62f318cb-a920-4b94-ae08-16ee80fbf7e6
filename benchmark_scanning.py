#!/usr/bin/env python3
"""Benchmark script to compare different file scanning methods."""

import tempfile
import time
from pathlib import Path

import numpy as np
import rasterio
from rasterio.crs import CRS
from rasterio.transform import from_bounds

from faninsar.datasets.base import RasterDataset


def create_test_files(
    temp_dir: Path, file_count: int = 50, file_size: int = 200
) -> list[Path]:
    """Create test TIFF files for benchmarking."""
    print(f"Creating {file_count} test files ({file_size}x{file_size} pixels each)...")

    test_files = []
    crs = CRS.from_epsg(4326)

    for i in range(file_count):
        file_path = temp_dir / f"test_{i:03d}.tif"

        # Create different bounds for each file
        bounds = (i * 0.1, i * 0.1, (i + 1) * 0.1, (i + 1) * 0.1)
        data = np.random.rand(file_size, file_size).astype(np.float32)

        height, width = data.shape
        transform = from_bounds(*bounds, width, height)

        with rasterio.open(
            file_path,
            "w",
            driver="GTiff",
            height=height,
            width=width,
            count=1,
            dtype=data.dtype,
            crs=crs,
            transform=transform,
        ) as dst:
            dst.write(data, 1)

        test_files.append(file_path)

    print(f"Created {len(test_files)} test files")
    return test_files


def benchmark_method(method_name: str, test_files: list[Path], **kwargs) -> float:
    """Benchmark a specific scanning method."""
    print(f"\n--- Testing {method_name} ---")

    start_time = time.time()

    try:
        ds = RasterDataset(paths=test_files, verbose=True, **kwargs)
        end_time = time.time()

        elapsed = end_time - start_time
        print(f"{method_name} completed in {elapsed:.2f} seconds")
        print(f"Found {len(ds)} files, {ds.files.valid.sum()} valid")

        # Verify results
        expected_columns = [
            "paths",
            "valid",
            "file_crs",
            "file_bounds",
            "file_res",
            "file_dtype",
            "file_nodata",
            "crs",
            "bounds",
            "res",
            "colormap",
        ]

        missing_columns = [
            col for col in expected_columns if col not in ds.files.columns
        ]
        if missing_columns:
            print(f"Warning: Missing columns: {missing_columns}")
        else:
            print("✓ All expected columns present")

        return elapsed

    except Exception as e:
        print(f"Error in {method_name}: {e}")
        return float("inf")


def run_benchmark(file_counts: list[int] = None):
    """Run benchmark with different file counts."""
    if file_counts is None:
        file_counts = [10, 25, 50, 100]

    results = {}

    for file_count in file_counts:
        print(f"\n{'=' * 60}")
        print(f"BENCHMARKING WITH {file_count} FILES")
        print(f"{'=' * 60}")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            # Use larger files to make I/O more significant
            file_size = 500 if file_count >= 50 else 200
            test_files = create_test_files(temp_path, file_count, file_size)

            # Test each method
            methods = {
                "Sequential": {"parallel_loading": False, "async_scanning": False},
                "Async": {"parallel_loading": False, "async_scanning": True},
                "Dask": {"parallel_loading": True, "async_scanning": False},
            }

            file_results = {}

            for method_name, kwargs in methods.items():
                try:
                    elapsed = benchmark_method(method_name, test_files, **kwargs)
                    file_results[method_name] = elapsed
                except Exception as e:
                    print(f"Failed to test {method_name}: {e}")
                    file_results[method_name] = float("inf")

            results[file_count] = file_results

            # Print summary for this file count
            print(f"\n--- Summary for {file_count} files ---")
            sorted_results = sorted(file_results.items(), key=lambda x: x[1])

            fastest_time = sorted_results[0][1]
            for method, elapsed in sorted_results:
                if elapsed == float("inf"):
                    print(f"{method:12}: FAILED")
                else:
                    speedup = fastest_time / elapsed if elapsed > 0 else 0
                    print(f"{method:12}: {elapsed:6.2f}s (speedup: {speedup:.2f}x)")

    # Print overall summary
    print(f"\n{'=' * 60}")
    print("OVERALL SUMMARY")
    print(f"{'=' * 60}")

    print(f"{'Files':<8} {'Sequential':<12} {'Async':<12} {'Dask':<12}")
    print("-" * 50)

    for file_count, file_results in results.items():
        seq_time = file_results.get("Sequential", float("inf"))
        async_time = file_results.get("Async", float("inf"))
        dask_time = file_results.get("Dask", float("inf"))

        def format_time(t):
            return f"{t:.2f}s" if t != float("inf") else "FAIL"

        print(
            f"{file_count:<8} {format_time(seq_time):<12} {format_time(async_time):<12} {format_time(dask_time):<12}"
        )

    return results


if __name__ == "__main__":
    print("File Scanning Benchmark")
    print("Comparing Sequential vs Async vs Dask methods")

    # Run benchmark with different file counts
    results = run_benchmark([25, 50, 100, 200])

    print("\nBenchmark completed!")
