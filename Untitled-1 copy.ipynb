from pathlib import Path

from faninsar import datasets, query

data_dir = Path("/Volumes/Data/GeoData/YNG/Sentinel1/Hyp3/descending_roi")




ds = datasets.HyP3S1(root_dir=data_dir, parallel_loading=True)

ds = datasets.HyP3S1(root_dir=data_dir, parallel_loading=False)

roi = query.BoundingBox(98.86517887, 38.78630936, 98.90998476, 38.83929150, crs=4326)
result = ds.query(roi, use_dask=True)
result

result.nbytes / 1024**3

result.boxes

